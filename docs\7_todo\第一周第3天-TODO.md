# 第一周第3天 TODO - 跨平台提示词同步完善

## 📋 基于需求分析的开发任务

根据对第2天和第3天需求的分析，以及当前代码实现状态的评估，制定以下详细的开发任务清单。

## 🔍 当前状态评估

### ✅ 已完成 (基础架构完整)
- **数据库系统**: Dexie.js + IndexedDB 完整实现
- **悬浮小球**: 紫色气泡，拖拽，智能定位
- **历史气泡**: HistoryBubble组件，数据展示
- **平台适配**: DeepSeek/Kimi基础适配器
- **存储服务**: ChatHistoryDexieService 完整API

### ❌ 需要完善 (核心功能缺失)
- **自动发送监听**: 目前只有手动存档按钮
- **跨平台UID共享**: 相同提示词应共享chat_uid
- **交互体验**: 缺少Toast提示和高亮效果
- **平台数据**: 缺少初始化的平台字典数据

## 📝 详细开发任务

### 🎯 任务1: 实现自动发送监听机制 [P0]

#### 1.1 基类发送监听框架
**文件**: `extension/src/content/adapters/base.ts`

- [ ] **添加发送监听方法**
  ```typescript
  protected setupSendListener(): void {
    // 通用发送按钮监听逻辑
    // 支持多种触发方式：按钮点击、Enter键等
  }
  
  protected async handleSendEvent(prompt: string): Promise<void> {
    // 统一的发送事件处理
    // 自动存储到IndexedDB
    // 生成或复用chat_uid
  }
  ```

- [ ] **在初始化时调用**
  ```typescript
  async initialize(): Promise<void> {
    // 现有初始化逻辑...
    this.setupSendListener() // 新增
  }
  ```

#### 1.2 DeepSeek发送监听实现
**文件**: `extension/src/content/adapters/deepseek.ts`

- [ ] **重写发送监听方法**
  ```typescript
  protected setupSendListener(): void {
    // DeepSeek特有的发送按钮选择器
    const sendButtons = document.querySelectorAll(this.selectors.sendButton)
    sendButtons.forEach(button => {
      button.addEventListener('click', this.onSendClick.bind(this))
    })
    
    // 监听Enter键发送
    const inputElement = document.querySelector(this.selectors.inputField)
    if (inputElement) {
      inputElement.addEventListener('keydown', this.onKeyDown.bind(this))
    }
  }
  
  private async onSendClick(): Promise<void> {
    const prompt = this.getCurrentPrompt()
    if (prompt.trim()) {
      await this.handleSendEvent(prompt)
    }
  }
  ```

#### 1.3 Kimi发送监听实现
**文件**: `extension/src/content/adapters/kimi.ts`

- [ ] **实现Kimi特有的发送监听**
  ```typescript
  protected setupSendListener(): void {
    // Kimi可能使用不同的发送机制
    // 需要适配其特殊的UI结构
  }
  ```

### 🎯 任务2: 实现跨平台chat_uid共享机制 [P0]

#### 2.1 扩展存储服务
**文件**: `extension/src/lib/storage/chatHistoryDexie.ts`

- [ ] **添加按提示词查询方法**
  ```typescript
  async findByChatPrompt(prompt: string): Promise<DatabaseResult<ChatHistory | null>> {
    try {
      await dexieDatabase.initialize()
      const result = await dexieDatabase.chatHistory
        .where('chat_prompt')
        .equals(prompt)
        .and(item => item.is_delete === 0)
        .first()
      
      return { success: true, data: result || null }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
  
  async findExistingChatUid(prompt: string): Promise<string | null> {
    const result = await this.findByChatPrompt(prompt)
    return result.success && result.data ? result.data.chat_uid : null
  }
  ```

- [ ] **修改create方法支持UID复用**
  ```typescript
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      // 检查是否存在相同提示词
      const existingUid = await this.findExistingChatUid(input.chat_prompt)
      
      const data: Omit<ChatHistory, 'id'> = {
        chat_prompt: input.chat_prompt,
        chat_answer: input.chat_answer || null,
        chat_uid: existingUid || input.chat_uid || Date.now().toString(),
        platform_id: input.platform_id,
        // ... 其他字段
      }
      
      // 存储逻辑...
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
  ```

#### 2.2 优化数据库索引
**文件**: `extension/src/lib/database/dexie.ts`

- [ ] **添加chat_prompt索引**
  ```typescript
  this.version(3).stores({
    chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
    platform: '++id, name, url, is_delete'
  }).upgrade(tx => {
    console.log('Upgrading database to version 3 - adding chat_prompt index...')
  })
  ```

### 🎯 任务3: 优化气泡交互体验 [P1]

#### 3.1 添加Toast提示组件
**文件**: `extension/src/components/Toast.ts` (新建)

- [ ] **创建Toast组件**
  ```typescript
  export class Toast {
    private container: HTMLElement | null = null
    
    show(message: string, type: 'success' | 'error' | 'info' = 'success', duration: number = 2000): void {
      // 创建Toast元素
      // 添加动画效果
      // 自动消失
    }
    
    private createToastElement(message: string, type: string): HTMLElement {
      // 创建美观的Toast UI
    }
  }
  ```

#### 3.2 增强HistoryBubble交互
**文件**: `extension/src/components/HistoryBubble.ts`

- [ ] **添加点击高亮效果**
  ```typescript
  private handleItemClick(chat: ChatHistoryWithPlatform): void {
    // 添加高亮样式
    const clickedItem = event.target.closest('.echosync-history-item')
    if (clickedItem) {
      clickedItem.classList.add('echosync-item-clicked')
      setTimeout(() => {
        clickedItem.classList.remove('echosync-item-clicked')
      }, 300)
    }
    
    // 显示Toast提示
    const toast = new Toast()
    toast.show('提示词已复制', 'success')
    
    // 触发原有事件
    const event = new CustomEvent('echosync:history-item-click', {
      detail: { chat }
    })
    document.dispatchEvent(event)
    
    this.hide()
  }
  ```

- [ ] **添加CSS样式**
  ```css
  .echosync-item-clicked {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: scale(1.02);
    transition: all 0.3s ease;
  }
  ```

### 🎯 任务4: 平台数据初始化 [P1]

#### 4.1 创建平台初始化服务
**文件**: `extension/src/lib/storage/platformDexie.ts` (新建)

- [ ] **实现平台服务**
  ```typescript
  export class PlatformDexieService {
    async initializeDefaultPlatforms(): Promise<void> {
      const platforms = [
        { id: 1, name: 'DeepSeek', url: 'https://chat.deepseek.com', icon: '/icons/deepseek.png' },
        { id: 2, name: 'Kimi', url: 'https://kimi.moonshot.cn', icon: '/icons/kimi.png' },
        { id: 3, name: 'ChatGPT', url: 'https://chat.openai.com', icon: '/icons/chatgpt.png' },
        { id: 4, name: 'Claude', url: 'https://claude.ai', icon: '/icons/claude.png' },
        { id: 5, name: 'Gemini', url: 'https://gemini.google.com', icon: '/icons/gemini.png' }
      ]
      
      for (const platform of platforms) {
        const existing = await dexieDatabase.platform.get(platform.id)
        if (!existing) {
          await dexieDatabase.platform.add(platform)
        }
      }
    }
  }
  ```

#### 4.2 在数据库初始化时调用
**文件**: `extension/src/lib/database/dexie.ts`

- [ ] **添加平台初始化**
  ```typescript
  async initialize(): Promise<void> {
    try {
      await this.open()
      
      // 初始化默认平台数据
      const platformService = new PlatformDexieService()
      await platformService.initializeDefaultPlatforms()
      
      console.log('EchoSync database initialized successfully')
    } catch (error) {
      console.error('Database initialization failed:', error)
      throw error
    }
  }
  ```

### 🎯 任务5: 完整性测试和验证 [P1]

#### 5.1 创建端到端测试脚本
**文件**: `extension/src/test/e2e-test.ts` (新建)

- [ ] **实现测试流程**
  ```typescript
  export class E2ETest {
    async testDeepSeekToKimiFlow(): Promise<boolean> {
      // 1. 模拟DeepSeek页面输入和发送
      // 2. 验证数据存储到IndexedDB
      // 3. 模拟Kimi页面悬浮显示
      // 4. 验证数据正确显示
      // 5. 模拟点击和复制
      // 6. 验证chat_uid复用
    }
  }
  ```

#### 5.2 添加调试工具
**文件**: `extension/src/lib/debug.ts` (新建)

- [ ] **创建调试助手**
  ```typescript
  export class DebugHelper {
    async logDatabaseState(): Promise<void> {
      // 输出数据库当前状态
    }
    
    async simulateUserFlow(): Promise<void> {
      // 模拟用户操作流程
    }
    
    async validateDataIntegrity(): Promise<boolean> {
      // 验证数据完整性
    }
  }
  ```

### 🎯 任务6: 性能优化 [P2]

#### 6.1 查询性能优化
- [ ] **实现查询缓存**
- [ ] **添加分页加载**
- [ ] **优化索引使用**

#### 6.2 UI性能优化
- [ ] **添加loading状态**
- [ ] **优化动画性能**
- [ ] **实现虚拟滚动**

## 🚀 开发计划

### 第1阶段 (核心功能) - 预计4小时
1. **自动发送监听** (1.5小时)
   - 基类框架 (30分钟)
   - DeepSeek实现 (45分钟)
   - Kimi实现 (15分钟)

2. **chat_uid共享** (1.5小时)
   - 存储服务扩展 (45分钟)
   - 数据库索引优化 (30分钟)
   - 测试验证 (15分钟)

3. **平台数据初始化** (1小时)
   - 平台服务创建 (30分钟)
   - 数据库集成 (30分钟)

### 第2阶段 (体验优化) - 预计2小时
1. **Toast提示系统** (45分钟)
2. **气泡交互增强** (45分钟)
3. **完整性测试** (30分钟)

### 第3阶段 (性能优化) - 预计1小时
1. **查询优化** (30分钟)
2. **UI优化** (30分钟)

## 📊 验收标准

### 功能验收 ✅
- [ ] DeepSeek发送后自动存储，无需手动点击存档
- [ ] Kimi悬浮显示最近10条，按时间正确排序
- [ ] 点击气泡有高亮效果，显示"已复制"Toast
- [ ] 相同提示词在不同平台共享chat_uid

### 性能验收 ⚡
- [ ] 气泡显示延迟 < 500ms
- [ ] 数据库操作 < 100ms
- [ ] UI动画流畅 > 30fps

### 用户体验验收 🎯
- [ ] 操作直观，无学习成本
- [ ] 所有交互有明确反馈
- [ ] 异常情况有友好提示

## 📈 进度跟踪

**当前进度**: 0/6 任务完成 (0%)

- [ ] 任务1: 自动发送监听 (P0)
- [ ] 任务2: chat_uid共享 (P0)
- [ ] 任务3: 交互体验优化 (P1)
- [ ] 任务4: 平台数据初始化 (P1)
- [ ] 任务5: 完整性测试 (P1)
- [ ] 任务6: 性能优化 (P2)

**预计完成时间**: 7小时
**建议分配**: 2-3个工作日完成
