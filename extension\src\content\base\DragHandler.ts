/**
 * 拖拽处理类
 * 从base.ts中提取的拖拽功能相关方法
 */
export class DragHandler {
  private bubble: HTMLElement | null = null
  private dragState = {
    isDragging: false,
    isLongPressing: false,
    dragStartTime: 0,
    dragStartX: 0,
    dragStartY: 0,
    bubbleStartX: 0,
    bubbleStartY: 0,
    longPressTimer: null as number | null,
    dragThreshold: 5
  }

  constructor(bubble: HTMLElement) {
    this.bubble = bubble
    this.setupDragEvents()
  }

  /**
   * 设置拖拽事件
   */
  private setupDragEvents(): void {
    if (!this.bubble) return

    // 鼠标事件
    this.bubble.addEventListener('mousedown', this.handleMouseDown.bind(this))
    document.addEventListener('mousemove', this.handleMouseMove.bind(this))
    document.addEventListener('mouseup', this.handleMouseUp.bind(this))

    // 触摸事件
    this.bubble.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false })
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    document.addEventListener('touchend', this.handleTouchEnd.bind(this))

    console.log('Drag events set up for floating bubble')
  }

  /**
   * 鼠标按下事件
   */
  private handleMouseDown(e: MouseEvent): void {
    this.handleDragStart(e.clientX, e.clientY)
  }

  /**
   * 触摸开始事件
   */
  private handleTouchStart(e: TouchEvent): void {
    e.preventDefault()
    const touch = e.touches[0]
    this.handleDragStart(touch.clientX, touch.clientY)
  }

  /**
   * 开始拖拽
   */
  private handleDragStart(clientX: number, clientY: number): void {
    if (!this.bubble) return

    this.dragState.dragStartTime = Date.now()
    this.dragState.dragStartX = clientX
    this.dragState.dragStartY = clientY

    const rect = this.bubble.getBoundingClientRect()
    this.dragState.bubbleStartX = rect.left
    this.dragState.bubbleStartY = rect.top

    // 设置长按定时器
    this.dragState.longPressTimer = window.setTimeout(() => {
      this.startDragMode()
    }, 500) // 500ms长按

    console.log('Drag start initiated')
  }

  /**
   * 鼠标移动事件
   */
  private handleMouseMove(e: MouseEvent): void {
    this.handleDragMove(e.clientX, e.clientY)
  }

  /**
   * 触摸移动事件
   */
  private handleTouchMove(e: TouchEvent): void {
    e.preventDefault()
    const touch = e.touches[0]
    this.handleDragMove(touch.clientX, touch.clientY)
  }

  /**
   * 拖拽移动
   */
  private handleDragMove(clientX: number, clientY: number): void {
    if (!this.bubble) return

    const deltaX = clientX - this.dragState.dragStartX
    const deltaY = clientY - this.dragState.dragStartY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // 如果移动距离超过阈值，取消长按并开始拖拽
    if (distance > this.dragState.dragThreshold && !this.dragState.isDragging) {
      if (this.dragState.longPressTimer) {
        clearTimeout(this.dragState.longPressTimer)
        this.dragState.longPressTimer = null
      }
      this.startDragMode()
    }

    // 如果正在拖拽，更新位置
    if (this.dragState.isDragging) {
      const newX = this.dragState.bubbleStartX + deltaX
      const newY = this.dragState.bubbleStartY + deltaY

      this.bubble.style.left = `${newX}px`
      this.bubble.style.top = `${newY}px`
      this.bubble.style.right = 'auto'
    }
  }

  /**
   * 鼠标释放事件
   */
  private handleMouseUp(): void {
    this.handleDragEnd()
  }

  /**
   * 触摸结束事件
   */
  private handleTouchEnd(): void {
    this.handleDragEnd()
  }

  /**
   * 结束拖拽
   */
  private handleDragEnd(): void {
    // 清除长按定时器
    if (this.dragState.longPressTimer) {
      clearTimeout(this.dragState.longPressTimer)
      this.dragState.longPressTimer = null
    }

    // 如果正在拖拽，执行边界回弹
    if (this.dragState.isDragging) {
      document.dispatchEvent(new CustomEvent('echosync:snap-to-boundary'))
      this.endDragMode()
    }

    console.log('Drag end')
  }

  /**
   * 开始拖拽模式
   */
  private startDragMode(): void {
    if (!this.bubble) return

    this.dragState.isDragging = true
    this.dragState.isLongPressing = true

    // 添加拖拽样式
    this.bubble.style.transform = 'scale(1.1)'
    this.bubble.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.3)'
    this.bubble.style.zIndex = '10001'
    this.bubble.style.cursor = 'grabbing'

    // 添加拖拽类
    this.bubble.classList.add('dragging')

    console.log('Drag mode started')
  }

  /**
   * 结束拖拽模式
   */
  private endDragMode(): void {
    if (!this.bubble) return

    this.dragState.isDragging = false
    this.dragState.isLongPressing = false

    // 恢复样式
    this.bubble.style.transform = 'scale(1)'
    this.bubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
    this.bubble.style.zIndex = '10000'
    this.bubble.style.cursor = 'pointer'

    // 移除拖拽类
    this.bubble.classList.remove('dragging')

    console.log('Drag mode ended')
  }

  /**
   * 获取拖拽状态
   */
  getDragState(): typeof this.dragState {
    return { ...this.dragState }
  }

  /**
   * 是否正在拖拽
   */
  isDragging(): boolean {
    return this.dragState.isDragging
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.dragState.longPressTimer) {
      clearTimeout(this.dragState.longPressTimer)
      this.dragState.longPressTimer = null
    }

    // 移除事件监听器
    if (this.bubble) {
      this.bubble.removeEventListener('mousedown', this.handleMouseDown.bind(this))
      this.bubble.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    }

    document.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this))
    document.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    document.removeEventListener('touchend', this.handleTouchEnd.bind(this))

    this.bubble = null
  }
}
