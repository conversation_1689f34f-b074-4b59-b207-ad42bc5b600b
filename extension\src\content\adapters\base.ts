import { AIPlatform, Conversation, Message } from '@/types'
import { chatHistoryService } from '@/lib/storage/chatHistoryDexie'
import { platformService } from '@/lib/storage/platformDexie'
import { HistoryBubble } from '@/components/HistoryBubble'
import { ChatHistoryWithPlatform, Platform } from '@/types/database'

export abstract class AIAdapter {
  platformName: string
  platform: AIPlatform
  selectors: {
    inputField: string
    sendButton: string
    messageContainer: string
  }

  // 通用UI组件
  protected floatingBubble: HTMLElement | null = null
  protected archiveButton: HTMLElement | null = null
  protected inputElement: HTMLElement | null = null
  protected sendButtonContainer: HTMLElement | null = null
  protected historyBubble: HistoryBubble | null = null

  // 存档状态管理
  protected currentPromptId: string = ''
  protected archivedPromptIds: Set<string> = new Set()
  protected lastInputValue: string = ''
  protected inputObserver: MutationObserver | null = null

  // 平台信息
  protected currentPlatform: Platform | null = null

  // 拖拽相关状态
  protected isDragging: boolean = false
  protected isLongPressing: boolean = false
  protected dragStartTime: number = 0
  protected dragStartX: number = 0
  protected dragStartY: number = 0
  protected bubbleStartX: number = 0
  protected bubbleStartY: number = 0
  protected longPressTimer: number | null = null
  protected originalPosition: { top: string, right: string, left: string } = { top: '20px', right: '20px', left: 'auto' }
  protected dragThreshold: number = 5 // 拖拽阈值，超过此距离才开始拖拽

  constructor() {
    this.platformName = ''
    this.platform = '' as AIPlatform
    this.selectors = {
      inputField: '',
      sendButton: '',
      messageContainer: ''
    }

    // 初始化通用功能
    this.initUniversalFeatures()
  }

  // 注入提示词到输入框
  abstract injectPrompt(prompt: string): Promise<void>

  // 提取当前对话内容
  abstract extractConversation(): Promise<Conversation | null>

  // 检查当前页面是否有效
  abstract isValidPage(): boolean

  // 等待元素出现
  protected waitForElement(selector: string, timeout = 5000): Promise<Element | null> {
    return new Promise((resolve) => {
      const element = document.querySelector(selector)
      if (element) {
        resolve(element)
        return
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector)
        if (element) {
          obs.disconnect()
          resolve(element)
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  // 模拟用户输入
  protected simulateUserInput(element: HTMLElement, text: string) {
    // 触发focus事件
    element.focus()

    // 设置值
    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
      const inputElement = element as HTMLInputElement
      inputElement.value = text
      
      // 触发input事件
      const inputEvent = new Event('input', { bubbles: true })
      element.dispatchEvent(inputEvent)
      
      // 触发change事件
      const changeEvent = new Event('change', { bubbles: true })
      element.dispatchEvent(changeEvent)
    } else if (element.contentEditable === 'true') {
      element.textContent = text
      
      // 触发input事件
      const inputEvent = new Event('input', { bubbles: true })
      element.dispatchEvent(inputEvent)
    }

    // 移动光标到末尾
    if (element.contentEditable === 'true') {
      const range = document.createRange()
      const selection = window.getSelection()
      range.selectNodeContents(element)
      range.collapse(false)
      selection?.removeAllRanges()
      selection?.addRange(range)
    }
  }

  // 模拟点击
  protected simulateClick(element: HTMLElement) {
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    })
    element.dispatchEvent(clickEvent)
  }

  // 获取输入框当前内容
  protected getCurrentInput(): string {
    const inputElement = document.querySelector(this.selectors.inputField) as HTMLElement
    if (!inputElement) return ''

    if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
      return (inputElement as HTMLInputElement).value
    } else if (inputElement.contentEditable === 'true') {
      return inputElement.textContent || ''
    }

    return ''
  }

  // 检查是否可以发送消息
  protected canSendMessage(): boolean {
    const sendButton = document.querySelector(this.selectors.sendButton) as HTMLButtonElement
    return sendButton && !sendButton.disabled
  }

  // 初始化通用功能
  protected async initUniversalFeatures() {
    console.log(`Starting initUniversalFeatures for ${this.platformName}`)

    // 等待页面加载完成
    await this.waitForPageLoad()
    console.log('Page load completed')

    // 延迟初始化确保页面完全渲染
    setTimeout(async () => {
      try {
        console.log(`Checking if page is valid for ${this.platformName}...`)
        const isValid = this.isValidPage()
        console.log(`Page validity check result: ${isValid}`)

        if (isValid) {
          console.log(`Initializing universal features for ${this.platformName}...`)

          // 创建浮动气泡
          this.createFloatingBubble()

          // 设置输入框聚焦监听
          await this.setupInputFocusListener()

          // 添加存档按钮
          await this.addArchiveButton()

          // 设置发送监听
          this.setupSendListener()

          // 加载当前平台信息
          await this.loadCurrentPlatform()

          console.log(`Universal features initialized for ${this.platformName}`)
        } else {
          console.log(`Page is not valid for ${this.platformName}, skipping initialization`)
        }
      } catch (error) {
        console.error(`Error initializing universal features for ${this.platformName}:`, error)
      }
    }, 2000)
  }

  // 等待页面加载完成
  protected waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve()
        return
      }

      const checkReady = () => {
        if (document.readyState === 'complete') {
          resolve()
        } else {
          setTimeout(checkReady, 100)
        }
      }

      checkReady()
    })
  }

  // 创建浮动气泡
  protected createFloatingBubble() {
    if (this.floatingBubble) {
      console.log('Floating bubble already exists, skipping creation')
      return
    }

    console.log('Creating floating bubble for platform:', this.platformName)
    this.floatingBubble = document.createElement('div')
    this.floatingBubble.id = 'echosync-floating-bubble'
    this.floatingBubble.innerHTML = `
      <div class="bubble-content">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
      </div>
    `

    // 设置样式
    this.floatingBubble.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
    `

    // 添加悬停效果和历史提示词显示
    let hoverTimer: number | null = null

    this.floatingBubble.addEventListener('mouseenter', () => {
      if (this.floatingBubble) {
        this.floatingBubble.style.transform = 'scale(1.1)'
        this.floatingBubble.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.25)'
      }

      // 延迟显示历史气泡
      hoverTimer = window.setTimeout(() => {
        this.showHistoryBubble()
      }, 500)
    })

    this.floatingBubble.addEventListener('mouseleave', () => {
      if (this.floatingBubble) {
        this.floatingBubble.style.transform = 'scale(1)'
        this.floatingBubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
      }

      // 清除定时器
      if (hoverTimer) {
        clearTimeout(hoverTimer)
        hoverTimer = null
      }

      // 延迟隐藏历史气泡，给用户时间移动鼠标
      setTimeout(() => {
        if (this.historyBubble && !this.isMouseOverHistoryBubble()) {
          this.historyBubble.hide()
        }
      }, 200)
    })

    // 添加点击事件
    this.floatingBubble.addEventListener('click', (e) => {
      // 如果是拖拽结束后的点击，不触发显示功能
      if (this.isDragging) {
        e.preventDefault()
        return
      }
      this.showStoredPrompts()
    })

    // 添加右键点击事件用于调试
    this.floatingBubble.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      this.debugFeatures()
    })

    // 添加拖拽事件监听器
    this.setupDragEvents()

    // 保存初始位置
    this.originalPosition = {
      top: '20px',
      right: '20px',
      left: 'auto'
    }

    // 初始化历史气泡
    this.initializeHistoryBubble()

    document.body.appendChild(this.floatingBubble)
    console.log('Floating bubble created and added to DOM:', this.floatingBubble)
  }

  // 设置拖拽事件
  protected setupDragEvents() {
    if (!this.floatingBubble) return

    // 鼠标事件
    this.floatingBubble.addEventListener('mousedown', (e) => this.handleDragStart(e))
    document.addEventListener('mousemove', (e) => this.handleDragMove(e))
    document.addEventListener('mouseup', () => this.handleDragEnd())

    // 触摸事件（移动端支持）
    this.floatingBubble.addEventListener('touchstart', (e) => this.handleDragStart(e.touches[0]))
    document.addEventListener('touchmove', (e) => this.handleDragMove(e.touches[0]))
    document.addEventListener('touchend', () => this.handleDragEnd())

    // 防止默认的拖拽行为
    this.floatingBubble.addEventListener('dragstart', (e) => e.preventDefault())
  }

  // 开始拖拽
  protected handleDragStart(e: MouseEvent | Touch) {
    if (!this.floatingBubble) return

    this.dragStartTime = Date.now()
    this.dragStartX = e.clientX
    this.dragStartY = e.clientY
    this.isLongPressing = true

    const rect = this.floatingBubble.getBoundingClientRect()
    this.bubbleStartX = rect.left
    this.bubbleStartY = rect.top

    // 立即添加轻微的按下效果
    this.floatingBubble.style.transform = 'scale(0.95)'
    this.floatingBubble.style.transition = 'transform 0.1s ease'

    // 设置长按定时器（300ms后开始拖拽模式，更快响应）
    this.longPressTimer = window.setTimeout(() => {
      if (this.isLongPressing) {
        this.isDragging = true
        this.startDragMode()
      }
    }, 300)

    // 添加视觉反馈
    this.floatingBubble.style.cursor = 'grabbing'
  }

  // 拖拽移动
  protected handleDragMove(e: MouseEvent | Touch) {
    if (!this.floatingBubble) return

    const deltaX = e.clientX - this.dragStartX
    const deltaY = e.clientY - this.dragStartY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // 如果还在长按状态，检查是否超过拖拽阈值
    if (this.isLongPressing && !this.isDragging && distance > this.dragThreshold) {
      // 取消长按定时器，立即进入拖拽模式
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer)
        this.longPressTimer = null
      }
      this.isDragging = true
      this.startDragMode()
    }

    // 只有在拖拽模式下才移动气泡
    if (!this.isDragging) return

    const newX = this.bubbleStartX + deltaX
    const newY = this.bubbleStartY + deltaY

    // 边界检查，增加弹性效果
    const bubbleSize = 80 // 拖拽时的大小
    const margin = 10 // 边界缓冲
    const maxX = window.innerWidth - bubbleSize + margin
    const maxY = window.innerHeight - bubbleSize + margin

    let constrainedX = Math.max(-margin, Math.min(newX, maxX))
    let constrainedY = Math.max(-margin, Math.min(newY, maxY))

    // 添加边界弹性效果
    if (newX < 0) constrainedX = newX * 0.3
    if (newX > window.innerWidth - bubbleSize) constrainedX = (window.innerWidth - bubbleSize) + (newX - (window.innerWidth - bubbleSize)) * 0.3
    if (newY < 0) constrainedY = newY * 0.3
    if (newY > window.innerHeight - bubbleSize) constrainedY = (window.innerHeight - bubbleSize) + (newY - (window.innerHeight - bubbleSize)) * 0.3

    this.floatingBubble.style.left = `${constrainedX}px`
    this.floatingBubble.style.top = `${constrainedY}px`
    this.floatingBubble.style.right = 'auto'
    this.floatingBubble.style.transition = 'none' // 拖拽时禁用过渡动画
  }

  // 结束拖拽
  protected handleDragEnd() {
    this.isLongPressing = false

    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }

    if (this.floatingBubble) {
      // 如果没有进入拖拽模式，恢复按下效果
      if (!this.isDragging) {
        this.floatingBubble.style.transform = 'scale(1)'
        this.floatingBubble.style.transition = 'transform 0.2s ease'
      }
    }

    if (this.isDragging) {
      this.endDragMode()

      // 边界回弹效果
      this.snapToBoundary()

      // 更新原始位置为当前位置
      if (this.floatingBubble) {
        // 延迟更新位置，等待回弹动画完成
        setTimeout(() => {
          if (this.floatingBubble) {
            this.originalPosition = {
              top: this.floatingBubble.style.top,
              right: this.floatingBubble.style.right,
              left: this.floatingBubble.style.left
            }
            console.log('Updated original position after drag:', this.originalPosition)
          }
        }, 300)
      }

      // 延迟重置拖拽状态，避免立即触发点击事件
      setTimeout(() => {
        this.isDragging = false
      }, 150)
    }

    if (this.floatingBubble) {
      this.floatingBubble.style.cursor = 'pointer'
    }
  }

  // 边界回弹效果
  protected snapToBoundary() {
    if (!this.floatingBubble) return

    const rect = this.floatingBubble.getBoundingClientRect()
    const bubbleSize = 60 // 正常大小
    let newX = rect.left
    let newY = rect.top

    // 检查边界并调整位置
    if (rect.left < 0) newX = 0
    if (rect.right > window.innerWidth) newX = window.innerWidth - bubbleSize
    if (rect.top < 0) newY = 0
    if (rect.bottom > window.innerHeight) newY = window.innerHeight - bubbleSize

    // 应用回弹动画
    this.floatingBubble.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    this.floatingBubble.style.left = `${newX}px`
    this.floatingBubble.style.top = `${newY}px`
  }

  // 开始拖拽模式
  protected startDragMode() {
    if (!this.floatingBubble) return

    // 保存当前位置作为原始位置
    this.originalPosition = {
      top: this.floatingBubble.style.top,
      right: this.floatingBubble.style.right,
      left: this.floatingBubble.style.left
    }

    // 拖拽时的视觉效果 - 更加动态和现代
    this.floatingBubble.style.transform = 'scale(1.2)'
    this.floatingBubble.style.boxShadow = '0 12px 48px rgba(139, 92, 246, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2)'
    this.floatingBubble.style.zIndex = '10001'
    this.floatingBubble.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease'

    // 添加轻微的脉冲效果
    this.floatingBubble.style.animation = 'echosync-pulse 1.5s ease-in-out infinite'

    // 添加脉冲动画样式
    if (!document.getElementById('echosync-drag-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-drag-styles'
      style.textContent = `
        @keyframes echosync-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.8; }
        }
      `
      document.head.appendChild(style)
    }

    console.log('Drag mode started')
  }

  // 结束拖拽模式
  protected endDragMode() {
    if (!this.floatingBubble) return

    // 恢复原始大小和效果
    this.floatingBubble.style.transform = 'scale(1)'
    this.floatingBubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
    this.floatingBubble.style.zIndex = '10000'
    this.floatingBubble.style.transition = 'all 0.3s ease'
    this.floatingBubble.style.animation = 'none'

    console.log('Drag mode ended')
  }

  // 设置输入框聚焦监听
  protected async setupInputFocusListener() {
    // 使用更灵活的方式查找输入框
    await this.findAndSetupInputElement()

    // 使用事件委托监听所有可能的输入框
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement
      if (this.isInputElement(target)) {
        this.inputElement = target
        console.log('Input focused:', target)
        this.moveToInputField()
      }
    })

    document.addEventListener('focusout', (event) => {
      const target = event.target as HTMLElement
      if (this.isInputElement(target)) {
        console.log('Input blurred:', target)
        setTimeout(() => this.moveToDefaultPosition(), 100) // 延迟一点避免闪烁
      }
    })

    // 监听页面变化，重新查找输入框
    const observer = new MutationObserver(() => {
      this.findAndSetupInputElement()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  // 查找并设置输入框元素
  protected async findAndSetupInputElement() {
    // 首先尝试使用平台特定的选择器
    let element = document.querySelector(this.selectors.inputField) as HTMLElement
    if (element && this.isVisibleElement(element)) {
      this.inputElement = element
      console.log('Found input element with platform selector:', this.selectors.inputField, element)
      return
    }

    // 如果平台特定选择器没找到，使用通用选择器
    const selectors = this.getUniversalInputSelectors()
    for (const selector of selectors) {
      element = document.querySelector(selector) as HTMLElement
      if (element && this.isVisibleElement(element)) {
        this.inputElement = element
        console.log('Found input element with universal selector:', selector, element)
        return
      }
    }

    // 如果没找到，等待一段时间后重试
    setTimeout(() => this.findAndSetupInputElement(), 1000)
  }

  // 获取通用输入框选择器（子类可以重写以添加平台特定的选择器）
  protected getUniversalInputSelectors(): string[] {
    return [
      'textarea[placeholder*="输入"]',
      'textarea[placeholder*="请输入"]',
      'textarea[placeholder*="Message"]',
      'textarea[placeholder*="Type"]',
      'textarea[placeholder*="Enter"]',
      'textarea',
      '[contenteditable="true"]',
      'input[type="text"]'
    ]
  }

  // 检查是否是输入元素
  protected isInputElement(element: HTMLElement): boolean {
    if (!element) return false

    const tagName = element.tagName.toLowerCase()
    const isTextarea = tagName === 'textarea'
    const isInput = tagName === 'input' && (element as HTMLInputElement).type === 'text'
    const isContentEditable = element.contentEditable === 'true'

    return isTextarea || isInput || isContentEditable
  }

  // 检查元素是否可见
  protected isVisibleElement(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)

    return rect.width > 0 &&
           rect.height > 0 &&
           style.display !== 'none' &&
           style.visibility !== 'hidden' &&
           style.opacity !== '0'
  }

  // 移动气泡到输入框左上方
  protected moveToInputField() {
    if (!this.floatingBubble) {
      console.warn('Floating bubble not found')
      return
    }

    if (!this.inputElement) {
      console.warn('Input element not found')
      return
    }

    const inputRect = this.inputElement.getBoundingClientRect()
    const bubbleSize = 60

    console.log('Moving bubble to input field:', {
      inputRect,
      bubbleSize,
      inputElement: this.inputElement
    })

    // 确保输入框在视口内
    if (inputRect.top < 0 || inputRect.bottom > window.innerHeight) {
      console.warn('Input element is outside viewport')
      return
    }

    // 计算气泡位置 - 移动到输入框左上方
    const bubbleTop = Math.max(10, inputRect.top - bubbleSize - 10)
    const bubbleLeft = Math.max(10, inputRect.left - 10) // 左上方，稍微偏左一点

    this.floatingBubble.style.position = 'fixed'
    this.floatingBubble.style.top = `${bubbleTop}px`
    this.floatingBubble.style.left = `${bubbleLeft}px`
    this.floatingBubble.style.right = 'auto'
    this.floatingBubble.style.transform = 'scale(1.1)' // 聚焦时稍微放大

    console.log('Bubble moved to input field (left-top):', { top: bubbleTop, left: bubbleLeft })
  }

  // 移动气泡到默认位置（原来的位置）
  protected moveToDefaultPosition() {
    if (!this.floatingBubble) {
      console.warn('Floating bubble not found')
      return
    }

    console.log('Moving bubble to original position:', this.originalPosition)

    this.floatingBubble.style.position = 'fixed'

    // 如果有保存的原始位置，使用原始位置；否则使用默认位置
    if (this.originalPosition.top && this.originalPosition.right && this.originalPosition.left) {
      this.floatingBubble.style.top = this.originalPosition.top
      this.floatingBubble.style.right = this.originalPosition.right
      this.floatingBubble.style.left = this.originalPosition.left
    } else {
      // 默认位置
      this.floatingBubble.style.top = '20px'
      this.floatingBubble.style.right = '20px'
      this.floatingBubble.style.left = 'auto'
    }

    this.floatingBubble.style.transform = 'scale(1)' // 恢复原始大小

    console.log('Bubble moved to original position')
  }

  // 添加存档按钮
  protected async addArchiveButton() {
    // 等待输入框出现
    const inputElement = await this.waitForElement(this.selectors.inputField) as HTMLElement
    if (!inputElement) return

    // 找到输入框的父容器
    const inputContainer = this.findInputContainer(inputElement)
    if (!inputContainer) return

    // 创建存档按钮
    this.archiveButton = document.createElement('div')
    this.archiveButton.id = 'echosync-archive-button'
    this.archiveButton.title = '存档提示词'
    this.archiveButton.innerHTML = `
      <div class="archive-icon">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    `

    // 设置存档按钮样式 - 与浮动小球一致
    this.archiveButton.style.cssText = `
      position: fixed;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
      box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
      cursor: pointer;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 0;
      transform: scale(0.8);
      pointer-events: none;
    `

    // 添加悬停效果
    this.archiveButton.addEventListener('mouseenter', () => {
      if (this.archiveButton && !this.archiveButton.classList.contains('archived')) {
        this.archiveButton.style.transform = 'scale(1.1)'
        this.archiveButton.style.boxShadow = '0 6px 30px rgba(139, 92, 246, 0.4)'
      }
    })

    this.archiveButton.addEventListener('mouseleave', () => {
      if (this.archiveButton && !this.archiveButton.classList.contains('archived')) {
        this.archiveButton.style.transform = 'scale(1)'
        this.archiveButton.style.boxShadow = '0 4px 20px rgba(139, 92, 246, 0.3)'
      }
    })

    // 添加点击事件
    this.archiveButton.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()
      this.archiveCurrentPrompt()
    })

    // 定位到输入框右侧
    this.positionArchiveButton(inputContainer)

    // 添加到页面
    document.body.appendChild(this.archiveButton)

    // 设置输入监听
    this.inputElement = inputElement
    this.setupInputMonitoring()

    // 初始化提示词ID
    this.generateNewPromptId()
  }

  // 查找输入框容器
  protected findInputContainer(inputElement: HTMLElement): HTMLElement | null {
    let container = inputElement.parentElement
    let attempts = 0
    const maxAttempts = 10

    while (container && attempts < maxAttempts) {
      // 查找包含输入框的合适容器
      if (container.offsetWidth > 200 && container.offsetHeight > 30) {
        return container
      }
      container = container.parentElement
      attempts++
    }

    return inputElement.parentElement
  }

  // 定位存档按钮到输入框右侧
  protected positionArchiveButton(inputContainer: HTMLElement) {
    if (!this.archiveButton) return

    const updatePosition = () => {
      const rect = inputContainer.getBoundingClientRect()
      const buttonSize = 50
      const margin = 15

      this.archiveButton!.style.left = `${rect.right + margin}px`
      this.archiveButton!.style.top = `${rect.top + (rect.height - buttonSize) / 2}px`
    }

    updatePosition()

    // 监听窗口大小变化和滚动
    window.addEventListener('resize', updatePosition)
    window.addEventListener('scroll', updatePosition)

    // 监听输入框位置变化
    const resizeObserver = new ResizeObserver(updatePosition)
    resizeObserver.observe(inputContainer)
  }

  // 设置输入监听
  protected setupInputMonitoring() {
    if (!this.inputElement) return

    // 监听输入变化
    const handleInputChange = () => {
      const currentValue = this.getCurrentInput()

      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue
        this.updateArchiveButtonState(currentValue)

        // 如果内容变化，生成新的提示词ID
        if (currentValue.trim()) {
          this.generateNewPromptId()
        }
      }
    }

    // 添加各种输入事件监听
    this.inputElement.addEventListener('input', handleInputChange)
    this.inputElement.addEventListener('keyup', handleInputChange)
    this.inputElement.addEventListener('paste', () => {
      setTimeout(handleInputChange, 100)
    })

    // 监听DOM变化（对于contenteditable元素）
    if (this.inputElement.contentEditable === 'true') {
      this.inputObserver = new MutationObserver(handleInputChange)
      this.inputObserver.observe(this.inputElement, {
        childList: true,
        subtree: true,
        characterData: true
      })
    }
  }

  // 生成新的提示词ID
  protected generateNewPromptId() {
    this.currentPromptId = `prompt-${Date.now()}`
  }

  // 设置发送监听
  protected setupSendListener() {
    // 监听发送按钮点击
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (this.isSendButton(target)) {
        this.handleSendEvent()
      }
    })

    // 监听回车键发送
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        const target = event.target as HTMLElement
        if (this.isInputElement(target)) {
          this.handleSendEvent()
        }
      }
    })
  }

  // 判断是否是发送按钮
  protected isSendButton(element: HTMLElement): boolean {
    if (!element) return false

    // 检查元素本身和父元素
    let current: HTMLElement | null = element
    let depth = 0
    const maxDepth = 3

    while (current && depth < maxDepth) {
      if (current.matches && current.matches(this.selectors.sendButton)) {
        return true
      }
      current = current.parentElement
      depth++
    }

    return false
  }



  // 处理发送事件
  protected async handleSendEvent() {
    // 获取发送前的输入内容
    const promptContent = this.getCurrentInput()

    if (promptContent && promptContent.trim().length > 0) {
      // 自动存储提示词
      await this.autoArchivePrompt(promptContent.trim())
    }

    // 延迟检查是否有新消息出现
    setTimeout(() => {
      this.checkForNewMessage()
    }, 1000)
  }

  // 检查是否有新消息
  protected checkForNewMessage() {
    // 这里可以根据不同平台实现具体的消息检测逻辑
    // 如果检测到新消息，生成新的提示词ID
    this.generateNewPromptId()

    // 清空输入框内容缓存
    this.lastInputValue = ''

    // 更新存档按钮状态
    this.updateArchiveButtonState('')
  }

  // 更新存档按钮状态
  protected updateArchiveButtonState(inputValue: string) {
    if (!this.archiveButton) return

    const hasContent = inputValue.trim().length > 0
    const isArchived = this.archivedPromptIds.has(this.currentPromptId)

    if (hasContent && !isArchived) {
      // 显示按钮并添加发光效果
      this.showArchiveButtonWithGlow()
    } else if (isArchived) {
      // 显示已存档状态
      this.showArchivedState()
    } else {
      // 隐藏按钮
      this.hideArchiveButton()
    }
  }

  // 显示存档按钮并添加发光效果
  protected showArchiveButtonWithGlow() {
    if (!this.archiveButton) return

    this.archiveButton.style.opacity = '1'
    this.archiveButton.style.transform = 'scale(1)'
    this.archiveButton.style.pointerEvents = 'auto'

    // 添加贝塞尔曲线发光动画
    this.archiveButton.style.animation = 'echosync-glow 2s ease-in-out infinite'
    this.archiveButton.classList.remove('archived')

    // 添加发光动画样式
    if (!document.getElementById('echosync-glow-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-glow-styles'
      style.textContent = `
        @keyframes echosync-glow {
          0%, 100% {
            box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
          }
          50% {
            box-shadow: 0 4px 30px rgba(139, 92, 246, 0.6), 0 0 20px rgba(139, 92, 246, 0.4);
          }
        }
      `
      document.head.appendChild(style)
    }
  }

  // 显示已存档状态
  protected showArchivedState() {
    if (!this.archiveButton) return

    this.archiveButton.style.opacity = '1'
    this.archiveButton.style.transform = 'scale(1)'
    this.archiveButton.style.pointerEvents = 'auto'
    this.archiveButton.style.animation = 'none'
    this.archiveButton.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%)'
    this.archiveButton.style.boxShadow = '0 4px 20px rgba(16, 185, 129, 0.3)'
    this.archiveButton.classList.add('archived')
  }

  // 隐藏存档按钮
  protected hideArchiveButton() {
    if (!this.archiveButton) return

    this.archiveButton.style.opacity = '0'
    this.archiveButton.style.transform = 'scale(0.8)'
    this.archiveButton.style.pointerEvents = 'none'
    this.archiveButton.style.animation = 'none'
  }

  // 自动存档提示词（发送时调用）
  protected async autoArchivePrompt(promptContent: string) {
    try {
      // 获取当前平台信息
      if (!this.currentPlatform) {
        await this.loadCurrentPlatform()
      }

      if (!this.currentPlatform) {
        console.warn('无法识别当前平台，跳过自动存档')
        return
      }

      // 生成新的提示词ID
      this.generateNewPromptId()

      // 直接使用 Dexie 存储
      const result = await chatHistoryService.create({
        chat_prompt: promptContent,
        chat_uid: this.currentPromptId,
        platform_id: this.currentPlatform.id!,
        create_time: Date.now()
      })

      if (result.success) {
        // 标记为已存档
        this.archivedPromptIds.add(this.currentPromptId)
        console.log('Prompt auto-archived:', result.data)
      } else {
        console.error('Auto archive failed:', result.error)
      }
    } catch (error) {
      console.error('Auto archive prompt error:', error)
    }
  }

  // 存档当前提示词
  protected async archiveCurrentPrompt() {
    const currentPrompt = this.getCurrentInput()
    if (!currentPrompt || currentPrompt.trim().length === 0) {
      this.showNotification('输入框为空，无法存档', 'error')
      return
    }

    try {
      // 获取当前平台信息
      if (!this.currentPlatform) {
        await this.loadCurrentPlatform()
      }

      if (!this.currentPlatform) {
        this.showNotification('无法识别当前平台', 'error')
        return
      }

      // 直接使用 Dexie 存储
      const result = await chatHistoryService.create({
        chat_prompt: currentPrompt,
        chat_uid: this.currentPromptId,
        platform_id: this.currentPlatform.id!,
        create_time: Date.now()
      })

      if (result.success) {
        // 标记为已存档
        this.archivedPromptIds.add(this.currentPromptId)

        // 更新按钮状态
        this.showArchivedState()

        // 显示成功通知和动画
        this.showNotification('提示词已存档', 'success')
        this.showArchiveAnimation()

        console.log('Prompt archived:', result.data)
      } else {
        this.showNotification(result.error || '存档失败', 'error')
      }
    } catch (error) {
      console.error('Archive prompt error:', error)
      this.showNotification('存档失败', 'error')
    }
  }

  // 保存提示词到storage
  protected async savePromptToStorage(prompt: string) {
    const promptData = {
      id: `prompt-${Date.now()}`,
      content: prompt,
      platform: this.platform,
      timestamp: Date.now(),
      url: window.location.href
    }

    // 获取现有的提示词列表
    const result = await chrome.storage.local.get(['archivedPrompts'])
    const archivedPrompts = result.archivedPrompts || []

    // 添加新提示词
    archivedPrompts.unshift(promptData)

    // 限制存储数量（最多100条）
    if (archivedPrompts.length > 100) {
      archivedPrompts.splice(100)
    }

    // 保存到storage
    await chrome.storage.local.set({ archivedPrompts })
  }

  // 显示存档动画
  protected showArchiveAnimation() {
    if (!this.archiveButton) return

    // 创建飞行动画元素
    const flyingIcon = this.archiveButton.cloneNode(true) as HTMLElement
    flyingIcon.style.cssText = `
      position: fixed;
      z-index: 10001;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    `

    const buttonRect = this.archiveButton.getBoundingClientRect()
    flyingIcon.style.left = `${buttonRect.left}px`
    flyingIcon.style.top = `${buttonRect.top}px`

    document.body.appendChild(flyingIcon)

    // 动画到气泡位置
    setTimeout(() => {
      if (this.floatingBubble) {
        const bubbleRect = this.floatingBubble.getBoundingClientRect()
        flyingIcon.style.left = `${bubbleRect.left + 15}px`
        flyingIcon.style.top = `${bubbleRect.top + 15}px`
        flyingIcon.style.transform = 'scale(0.5)'
        flyingIcon.style.opacity = '0'
      }
    }, 50)

    // 清理动画元素
    setTimeout(() => {
      document.body.removeChild(flyingIcon)
    }, 650)
  }

  // 显示已存储的提示词
  protected async showStoredPrompts() {
    try {
      // 使用新的存储系统获取去重的聊天历史
      const result = await chatHistoryService.getUniqueChats({ limit: 20 })

      if (result.success && result.data && result.data.length > 0) {
        // 更新历史气泡并显示
        if (this.historyBubble) {
          this.historyBubble.updateHistory(result.data)
          this.historyBubble.show(this.floatingBubble!)
        }
      } else {
        this.showNotification('暂无存档的提示词', 'info')
      }
    } catch (error) {
      console.error('Show stored prompts error:', error)
      this.showNotification('获取存档提示词失败', 'error')
    }
  }

  // 显示通知
  protected showNotification(message: string, type: 'success' | 'error' | 'info' = 'success') {
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      z-index: 10002;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      background-color: ${
        type === 'success' ? '#10b981' :
        type === 'error' ? '#ef4444' :
        '#3b82f6'
      };
    `
    notification.textContent = message

    document.body.appendChild(notification)

    // 3秒后自动移除
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateX(-50%) translateY(-20px)'
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  // 调试功能
  protected debugFeatures() {
    console.log(`=== ${this.platformName} Adapter Debug Info ===`)
    console.log('Floating bubble:', this.floatingBubble)
    console.log('Input element:', this.inputElement)
    console.log('Archive button:', this.archiveButton)
    console.log('Send button container:', this.sendButtonContainer)
    console.log('Original position:', this.originalPosition)
    console.log('Is dragging:', this.isDragging)
    console.log('Platform selectors:', this.selectors)

    // 查找所有可能的输入框
    const textareas = document.querySelectorAll('textarea')
    const contentEditables = document.querySelectorAll('[contenteditable="true"]')
    const inputs = document.querySelectorAll('input[type="text"]')

    console.log('Found textareas:', textareas)
    console.log('Found contenteditable elements:', contentEditables)
    console.log('Found text inputs:', inputs)

    // 测试移动功能
    if (this.inputElement) {
      console.log('Testing bubble movement...')
      this.moveToInputField()
      setTimeout(() => {
        this.moveToDefaultPosition()
      }, 2000)
    } else {
      console.warn('No input element found for testing')
      // 尝试重新查找
      this.findAndSetupInputElement()
    }

    // 测试拖拽功能
    console.log('Testing drag functionality...')
    this.startDragMode()
    setTimeout(() => {
      this.endDragMode()
    }, 1000)

    this.showNotification(`${this.platformName} 调试信息已输出到控制台`, 'info')
  }

  // 初始化历史气泡
  protected initializeHistoryBubble() {
    if (this.historyBubble) return

    this.historyBubble = new HistoryBubble({
      maxItems: 10,
      maxWidth: 320,
      showPlatformIcons: true
    })

    // 监听历史项点击事件
    document.addEventListener('echosync:history-item-click', (event: any) => {
      const { chat } = event.detail
      this.handleHistoryItemClick(chat)
    })
  }

  // 显示历史气泡
  protected async showHistoryBubble() {
    if (!this.historyBubble || !this.floatingBubble) return

    try {
      // 获取最近的聊天历史
      const result = await chatHistoryService.getUniqueChats({
        limit: 10,
        order_direction: 'DESC'
      })

      if (result.success && result.data) {
        this.historyBubble.updateHistory(result.data)
        this.historyBubble.show(this.floatingBubble)
      }
    } catch (error) {
      console.error('Failed to show history bubble:', error)
    }
  }

  // 检查鼠标是否在历史气泡上
  protected isMouseOverHistoryBubble(): boolean {
    if (!this.historyBubble) return false

    // 这里可以添加更精确的鼠标位置检测
    // 暂时返回气泡的可见状态
    return this.historyBubble.visible
  }

  // 处理历史项点击
  protected handleHistoryItemClick(chat: ChatHistoryWithPlatform) {
    // 将提示词填入输入框
    const inputElement = this.inputElement || document.querySelector(this.selectors.inputField) as HTMLElement
    if (inputElement) {
      if (inputElement.tagName.toLowerCase() === 'textarea' || inputElement.tagName.toLowerCase() === 'input') {
        (inputElement as HTMLInputElement).value = chat.chat_prompt
        inputElement.dispatchEvent(new Event('input', { bubbles: true }))
      } else if (inputElement.contentEditable === 'true') {
        inputElement.textContent = chat.chat_prompt
        inputElement.dispatchEvent(new Event('input', { bubbles: true }))
      }

      // 聚焦到输入框
      inputElement.focus()

      // 显示成功提示
      this.showNotification('提示词已填入输入框', 'success')
    }
  }

  // 加载当前平台信息
  protected async loadCurrentPlatform() {
    try {
      const hostname = window.location.hostname
      const result = await platformService.findByDomain(hostname)
      if (result.success && result.data) {
        this.currentPlatform = result.data
      } else {
        // 如果找不到平台，尝试根据平台名称查找
        const platformResult = await platformService.getByName(this.platform)
        if (platformResult.success && platformResult.data) {
          this.currentPlatform = platformResult.data
        }
      }
    } catch (error) {
      console.error('Failed to load current platform:', error)
    }
  }
}
