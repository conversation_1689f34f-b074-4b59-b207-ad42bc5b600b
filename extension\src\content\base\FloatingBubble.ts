import { HistoryBubble } from '@/components/HistoryBubble'

/**
 * 悬浮气泡管理类
 * 从base.ts中提取的悬浮气泡相关方法
 */
export class FloatingBubble {
  private bubble: HTMLElement | null = null
  private historyBubble: HistoryBubble | null = null
  private originalPosition = { top: '20px', right: '20px', left: 'auto' }

  constructor() {
    this.initializeHistoryBubble()
  }

  /**
   * 创建浮动气泡
   */
  createFloatingBubble(): HTMLElement | null {
    if (this.bubble) {
      console.log('Floating bubble already exists, skipping creation')
      return this.bubble
    }

    console.log('Creating floating bubble...')

    this.bubble = document.createElement('div')
    this.bubble.className = 'echosync-floating-bubble'
    this.bubble.innerHTML = '💬'

    // 恢复保存的位置
    const savedPosition = localStorage.getItem('echosync-bubble-position')
    if (savedPosition) {
      try {
        const position = JSON.parse(savedPosition)
        this.originalPosition = position
      } catch (e) {
        console.warn('Failed to parse saved position:', e)
      }
    }

    // 设置样式
    this.bubble.style.cssText = `
      position: fixed;
      top: ${this.originalPosition.top};
      right: ${this.originalPosition.right};
      left: ${this.originalPosition.left};
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
    `

    // 添加悬停效果和历史提示词显示
    let hoverTimer: number | null = null

    this.bubble.addEventListener('mouseenter', () => {
      if (this.bubble) {
        this.bubble.style.transform = 'scale(1.1)'
        this.bubble.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.25)'
      }

      // 延迟显示历史气泡
      hoverTimer = window.setTimeout(() => {
        this.showHistoryBubble()
      }, 500)
    })

    this.bubble.addEventListener('mouseleave', () => {
      if (this.bubble) {
        this.bubble.style.transform = 'scale(1)'
        this.bubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
      }

      // 清除定时器
      if (hoverTimer) {
        clearTimeout(hoverTimer)
        hoverTimer = null
      }

      // 延迟隐藏历史气泡，给用户时间移动到气泡上
      setTimeout(() => {
        if (!this.isMouseOverHistoryBubble()) {
          this.historyBubble?.hide()
        }
      }, 300)
    })

    document.body.appendChild(this.bubble)
    console.log('Floating bubble created and added to DOM:', this.bubble)

    return this.bubble
  }

  /**
   * 移动气泡到输入框左上方
   */
  moveToInputField(inputElement?: HTMLElement): void {
    if (!this.bubble) {
      console.warn('Floating bubble not found')
      return
    }

    if (!inputElement) {
      console.warn('Input element not provided')
      return
    }

    const rect = inputElement.getBoundingClientRect()
    const bubbleSize = 60
    const margin = 10

    // 计算位置（输入框左上方）
    const left = Math.max(margin, rect.left - bubbleSize - margin)
    const top = Math.max(margin, rect.top - bubbleSize - margin)

    this.bubble.style.left = `${left}px`
    this.bubble.style.top = `${top}px`
    this.bubble.style.right = 'auto'

    this.bubble.style.transform = 'scale(0.9)' // 稍微缩小

    console.log('Bubble moved to input field')
  }

  /**
   * 移动气泡到默认位置（原来的位置）
   */
  moveToDefaultPosition(): void {
    if (!this.bubble) {
      console.warn('Floating bubble not found')
      return
    }

    this.bubble.style.top = this.originalPosition.top
    this.bubble.style.right = this.originalPosition.right
    this.bubble.style.left = this.originalPosition.left

    this.bubble.style.transform = 'scale(1)' // 恢复原始大小

    console.log('Bubble moved to original position')
  }

  /**
   * 边界回弹效果
   */
  snapToBoundary(): void {
    if (!this.bubble) return

    const rect = this.bubble.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const bubbleSize = 60
    const margin = 10

    let newLeft = rect.left
    let newTop = rect.top

    // 水平边界检查
    if (rect.left < margin) {
      newLeft = margin
    } else if (rect.right > windowWidth - margin) {
      newLeft = windowWidth - bubbleSize - margin
    }

    // 垂直边界检查
    if (rect.top < margin) {
      newTop = margin
    } else if (rect.bottom > windowHeight - margin) {
      newTop = windowHeight - bubbleSize - margin
    }

    // 应用新位置
    this.bubble.style.left = `${newLeft}px`
    this.bubble.style.top = `${newTop}px`
    this.bubble.style.right = 'auto'

    // 保存位置
    this.originalPosition = {
      top: `${newTop}px`,
      left: `${newLeft}px`,
      right: 'auto'
    }

    localStorage.setItem('echosync-bubble-position', JSON.stringify(this.originalPosition))
  }

  /**
   * 初始化历史气泡
   */
  private initializeHistoryBubble(): void {
    if (this.historyBubble) return

    this.historyBubble = new HistoryBubble({
      maxItems: 10,
      maxWidth: 320,
      showPlatformIcons: true
    })

    // 监听历史项点击事件
    document.addEventListener('echosync:history-item-click', (event: any) => {
      const { chat } = event.detail
      // 这里需要回调到主类处理
      document.dispatchEvent(new CustomEvent('echosync:handle-history-click', { detail: { chat } }))
    })
  }

  /**
   * 显示历史气泡
   */
  private async showHistoryBubble(): Promise<void> {
    if (!this.historyBubble || !this.bubble) return

    // 触发获取历史数据的事件
    document.dispatchEvent(new CustomEvent('echosync:request-history-data'))
  }

  /**
   * 检查鼠标是否在历史气泡上
   */
  private isMouseOverHistoryBubble(): boolean {
    if (!this.historyBubble) return false
    return this.historyBubble.visible
  }

  /**
   * 获取气泡元素
   */
  getBubble(): HTMLElement | null {
    return this.bubble
  }

  /**
   * 获取历史气泡
   */
  getHistoryBubble(): HistoryBubble | null {
    return this.historyBubble
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.bubble) {
      this.bubble.remove()
      this.bubble = null
    }
    if (this.historyBubble) {
      this.historyBubble.destroy()
      this.historyBubble = null
    }
  }
}
