import { AIAdapter } from './base'
import { Conversation, Message, AIPlatform } from '@/types'

export class <PERSON><PERSON><PERSON>pter extends AIAdapter {
  constructor() {
    super()
    this.platformName = 'Claude'
    this.platform = 'claude'
    this.selectors = {
      inputField: 'div[contenteditable="true"]',
      sendButton: 'button[aria-label*="Send"], button[type="submit"]',
      messageContainer: '.font-claude-message, [data-is-streaming], .message'
    }
  }

  // 重写获取通用输入框选择器，添加Claude特有的选择器
  protected getUniversalInputSelectors(): string[] {
    return [
      // Claude特有的选择器
      'div[contenteditable="true"]',
      'div[data-testid="chat-input"]',
      'div[role="textbox"]',
      // 通用选择器
      ...super.getUniversalInputSelectors()
    ]
  }

  async injectPrompt(prompt: string): Promise<void> {
    const inputElement = await this.waitForElement(this.selectors.inputField) as HTMLElement
    if (!inputElement) {
      throw new Error('Claude input field not found')
    }

    this.simulateUserInput(inputElement, prompt)
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []
      
      messageElements.forEach((element, index) => {
        // Claude使用特定的类名来区分消息类型
        const isUser = element.closest('[data-is-streaming="false"]')?.querySelector('[data-testid="user-message"]') !== null ||
                      element.classList.contains('user-message') ||
                      element.getAttribute('data-message-role') === 'user'

        const contentElement = element.querySelector('.font-claude-message') || 
                              element.querySelector('[data-testid="message-content"]') || 
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content && content.length > 0) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 尝试获取对话标题
      const titleElement = document.querySelector('h1') || 
                          document.querySelector('[data-testid="conversation-title"]') ||
                          document.querySelector('.conversation-title')
      const title = titleElement?.textContent || `Claude对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `claude-${Date.now()}`,
        platform: 'claude',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('Extract Claude conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    return window.location.hostname === 'claude.ai' && 
           (window.location.pathname.startsWith('/chat') || window.location.pathname.startsWith('/c/'))
  }
}
