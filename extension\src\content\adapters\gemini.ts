import { AIAdapter } from './base'
import { Conversation, Message, AIPlatform } from '@/types'

export class GeminiAdapter extends AIAdapter {
  constructor() {
    super()
    this.platformName = 'Gemini'
    this.platform = 'gemini'
    this.selectors = {
      inputField: '.ql-editor, [contenteditable="true"]',
      sendButton: 'button[aria-label*="Send"], button[type="submit"]',
      messageContainer: '.conversation-container, .message'
    }
  }

  // 重写获取通用输入框选择器，添加Gemini特有的选择器
  protected getUniversalInputSelectors(): string[] {
    return [
      // Gemini特有的选择器
      'rich-textarea',
      '.ql-editor',
      'div[data-test-id="chat-input"]',
      'div[role="textbox"]',
      // 通用选择器
      ...super.getUniversalInputSelectors()
    ]
  }

  async injectPrompt(prompt: string): Promise<void> {
    // Gemini可能使用rich-textarea组件
    let inputElement = await this.waitForElement('rich-textarea') as HTMLElement
    
    if (!inputElement) {
      // 尝试查找普通的textarea
      inputElement = await this.waitForElement('textarea') as HTMLElement
    }

    if (!inputElement) {
      throw new Error('Gemini input field not found')
    }

    // 对于rich-textarea，可能需要特殊处理
    if (inputElement.tagName.toLowerCase() === 'rich-textarea') {
      // 尝试找到内部的可编辑元素
      const editableElement = inputElement.querySelector('[contenteditable="true"]') || 
                             inputElement.querySelector('textarea') ||
                             inputElement

      this.simulateUserInput(editableElement as HTMLElement, prompt)
    } else {
      this.simulateUserInput(inputElement, prompt)
    }

    await new Promise(resolve => setTimeout(resolve, 100))
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []
      
      messageElements.forEach((element, index) => {
        // Gemini的消息结构可能比较复杂
        const isUser = element.querySelector('[data-test-id="user-message"]') !== null ||
                      element.classList.contains('user-message') ||
                      element.getAttribute('data-message-author') === 'user'

        const contentElement = element.querySelector('[data-test-id="message-content"]') || 
                              element.querySelector('.message-content') || 
                              element.querySelector('.markdown-content') ||
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content && content.length > 0) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      const title = `Gemini对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `gemini-${Date.now()}`,
        platform: 'gemini',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('Extract Gemini conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    return window.location.hostname === 'gemini.google.com'
  }
}
